#include "xml_wrapper.hpp"
#include <algorithm>
#include <iomanip>
#include <fstream>

namespace xml_cpp {

// Attribute implementation
int Attribute::int_value(int default_val) const {
    if (!attr_) return default_val;
    int result;
    if (attr_->QueryIntValue(&result) == tinyxml2::XML_SUCCESS) {
        return result;
    }
    return default_val;
}

double Attribute::double_value(double default_val) const {
    if (!attr_) return default_val;
    double result;
    if (attr_->QueryDoubleValue(&result) == tinyxml2::XML_SUCCESS) {
        return result;
    }
    return default_val;
}

bool Attribute::bool_value(bool default_val) const {
    if (!attr_) return default_val;
    bool result;
    if (attr_->QueryBoolValue(&result) == tinyxml2::XML_SUCCESS) {
        return result;
    }
    return default_val;
}

void Attribute::set_value(const std::string& value) {
    if (attr_ && elem_) {
        elem_->SetAttribute(attr_->Name(), value.c_str());
    }
}

void Attribute::set_value(int value) {
    if (attr_ && elem_) {
        elem_->SetAttribute(attr_->Name(), value);
    }
}

void Attribute::set_value(double value) {
    if (attr_ && elem_) {
        elem_->SetAttribute(attr_->Name(), value);
    }
}

void Attribute::set_value(bool value) {
    if (attr_ && elem_) {
        elem_->SetAttribute(attr_->Name(), value);
    }
}

// Text implementation
void Text::set_value(const std::string& value) {
    if (text_) {
        text_->SetValue(value.c_str());
    }
}

// Element implementation
Element::Element(tinyxml2::XMLElement* elem, std::shared_ptr<Document> doc)
    : elem_(elem), doc_(doc) {
}

bool Element::has_attribute(const std::string& name) const {
    return elem_ && elem_->Attribute(name.c_str()) != nullptr;
}

Attribute Element::get_attribute(const std::string& name) const {
    if (!elem_) return Attribute(nullptr);
    return Attribute(const_cast<tinyxml2::XMLAttribute*>(elem_->FindAttribute(name.c_str())), elem_);
}

std::string Element::get_attribute_value(const std::string& name, const std::string& default_val) const {
    if (!elem_) return default_val;
    const char* value = elem_->Attribute(name.c_str());
    return value ? value : default_val;
}

Element& Element::set_attribute(const std::string& name, const std::string& value) {
    if (elem_) {
        elem_->SetAttribute(name.c_str(), value.c_str());
    }
    return *this;
}

Element& Element::set_attribute(const std::string& name, int value) {
    if (elem_) {
        elem_->SetAttribute(name.c_str(), value);
    }
    return *this;
}

Element& Element::set_attribute(const std::string& name, double value) {
    if (elem_) {
        elem_->SetAttribute(name.c_str(), value);
    }
    return *this;
}

Element& Element::set_attribute(const std::string& name, bool value) {
    if (elem_) {
        elem_->SetAttribute(name.c_str(), value);
    }
    return *this;
}

Element& Element::remove_attribute(const std::string& name) {
    if (elem_) {
        elem_->DeleteAttribute(name.c_str());
    }
    return *this;
}

Element& Element::set_text(const std::string& text) {
    if (elem_) {
        elem_->SetText(text.c_str());
    }
    return *this;
}

Element& Element::append_text(const std::string& text) {
    if (elem_ && doc_) {
        auto* doc = doc_->get_tinyxml_document();
        auto* text_node = doc->NewText(text.c_str());
        elem_->InsertEndChild(text_node);
    }
    return *this;
}

Element Element::first_child(const std::string& name) const {
    if (!elem_) return Element(nullptr, doc_);
    
    tinyxml2::XMLElement* child = name.empty() ? 
        elem_->FirstChildElement() : 
        elem_->FirstChildElement(name.c_str());
    
    return Element(child, doc_);
}

Element Element::next_sibling(const std::string& name) const {
    if (!elem_) return Element(nullptr, doc_);
    
    tinyxml2::XMLElement* sibling = name.empty() ? 
        elem_->NextSiblingElement() : 
        elem_->NextSiblingElement(name.c_str());
    
    return Element(sibling, doc_);
}

Element Element::parent() const {
    if (!elem_) return Element(nullptr, doc_);
    
    tinyxml2::XMLElement* parent = elem_->Parent() ? elem_->Parent()->ToElement() : nullptr;
    return Element(parent, doc_);
}

std::vector<Element> Element::children(const std::string& name) const {
    std::vector<Element> result;
    if (!elem_) return result;
    
    for (tinyxml2::XMLElement* child = elem_->FirstChildElement(name.empty() ? nullptr : name.c_str());
         child != nullptr;
         child = child->NextSiblingElement(name.empty() ? nullptr : name.c_str())) {
        result.emplace_back(child, doc_);
    }
    
    return result;
}

Element Element::append_child(const std::string& name) {
    if (!elem_ || !doc_) return Element(nullptr, doc_);
    
    auto* doc = doc_->get_tinyxml_document();
    auto* child = doc->NewElement(name.c_str());
    elem_->InsertEndChild(child);
    
    return Element(child, doc_);
}

Element Element::prepend_child(const std::string& name) {
    if (!elem_ || !doc_) return Element(nullptr, doc_);
    
    auto* doc = doc_->get_tinyxml_document();
    auto* child = doc->NewElement(name.c_str());
    elem_->InsertFirstChild(child);
    
    return Element(child, doc_);
}

Element Element::insert_child_after(const Element& after, const std::string& name) {
    if (!elem_ || !doc_ || !after.elem_) return Element(nullptr, doc_);
    
    auto* doc = doc_->get_tinyxml_document();
    auto* child = doc->NewElement(name.c_str());
    elem_->InsertAfterChild(after.elem_, child);
    
    return Element(child, doc_);
}

Element Element::insert_child_before(const Element& before, const std::string& name) {
    if (!elem_ || !doc_ || !before.elem_) return Element(nullptr, doc_);
    
    auto* doc = doc_->get_tinyxml_document();
    auto* child = doc->NewElement(name.c_str());
    elem_->InsertAfterChild(before.elem_, child);
    
    return Element(child, doc_);
}

void Element::remove_child(const Element& child) {
    if (elem_ && child.elem_) {
        elem_->DeleteChild(child.elem_);
    }
}

void Element::remove_children(const std::string& name) {
    if (!elem_) return;
    
    std::vector<tinyxml2::XMLElement*> to_remove;
    for (tinyxml2::XMLElement* child = elem_->FirstChildElement(name.empty() ? nullptr : name.c_str());
         child != nullptr;
         child = child->NextSiblingElement(name.empty() ? nullptr : name.c_str())) {
        to_remove.push_back(child);
    }
    
    for (auto* child : to_remove) {
        elem_->DeleteChild(child);
    }
}

void Element::remove_from_parent() {
    if (elem_ && elem_->Parent()) {
        elem_->Parent()->DeleteChild(elem_);
        elem_ = nullptr;
    }
}

size_t Element::child_count() const {
    if (!elem_) return 0;
    
    size_t count = 0;
    for (tinyxml2::XMLElement* child = elem_->FirstChildElement();
         child != nullptr;
         child = child->NextSiblingElement()) {
        ++count;
    }
    
    return count;
}

// Simple XPath-like find implementation
Element Element::find(const std::string& path) const {
    if (!elem_ || path.empty()) return Element(nullptr, doc_);
    
    // Simple implementation - just handle direct child paths like "child/grandchild"
    std::vector<std::string> parts;
    std::stringstream ss(path);
    std::string part;
    
    while (std::getline(ss, part, '/')) {
        if (!part.empty()) {
            parts.push_back(part);
        }
    }
    
    tinyxml2::XMLElement* current = elem_;
    for (const auto& part : parts) {
        if (!current) break;
        current = current->FirstChildElement(part.c_str());
    }
    
    return Element(current, doc_);
}

std::vector<Element> Element::find_all(const std::string& path) const {
    std::vector<Element> result;
    if (!elem_ || path.empty()) return result;
    
    // Simple implementation - find all elements with the given name
    std::function<void(tinyxml2::XMLElement*)> search = [&](tinyxml2::XMLElement* node) {
        if (!node) return;
        
        if (node->Name() == path) {
            result.emplace_back(node, doc_);
        }
        
        for (tinyxml2::XMLElement* child = node->FirstChildElement();
             child != nullptr;
             child = child->NextSiblingElement()) {
            search(child);
        }
    };
    
    search(elem_);
    return result;
}

Element& Element::set_namespace(const Namespace& ns) {
    if (elem_ && !ns.prefix.empty() && !ns.uri.empty()) {
        std::string xmlns_attr = "xmlns:" + ns.prefix;
        elem_->SetAttribute(xmlns_attr.c_str(), ns.uri.c_str());
    }
    return *this;
}

std::string Element::get_namespace_uri() const {
    // Simple implementation - look for xmlns attributes
    if (!elem_) return "";
    
    // This is a simplified implementation
    // In a full implementation, you'd need to walk up the tree
    // and handle namespace inheritance properly
    return "";
}

std::string Element::get_namespace_prefix() const {
    if (!elem_) return "";
    
    std::string name = elem_->Name();
    size_t colon_pos = name.find(':');
    if (colon_pos != std::string::npos) {
        return name.substr(0, colon_pos);
    }
    
    return "";
}

// Document implementation
Document::Document() : doc_(std::make_unique<tinyxml2::XMLDocument>()) {
}

Document::Document(const std::string& xml_content) : Document() {
    parse(xml_content);
}

Document::Document(Document&& other) noexcept
    : doc_(std::move(other.doc_)), namespaces_(std::move(other.namespaces_)) {
}

Document& Document::operator=(Document&& other) noexcept {
    if (this != &other) {
        doc_ = std::move(other.doc_);
        namespaces_ = std::move(other.namespaces_);
    }
    return *this;
}

void Document::parse(const std::string& xml_content) {
    if (!doc_) {
        doc_ = std::make_unique<tinyxml2::XMLDocument>();
    }

    tinyxml2::XMLError error = doc_->Parse(xml_content.c_str());
    if (error != tinyxml2::XML_SUCCESS) {
        throw ParseException("Failed to parse XML: " + std::string(doc_->ErrorStr()),
                           doc_->ErrorLineNum());
    }
}

void Document::load_file(const std::string& filename) {
    if (!doc_) {
        doc_ = std::make_unique<tinyxml2::XMLDocument>();
    }

    tinyxml2::XMLError error = doc_->LoadFile(filename.c_str());
    if (error != tinyxml2::XML_SUCCESS) {
        throw ParseException("Failed to load XML file '" + filename + "': " +
                           std::string(doc_->ErrorStr()), doc_->ErrorLineNum());
    }
}

std::string Document::to_string(bool compact) const {
    if (!doc_) return "";

    tinyxml2::XMLPrinter printer(nullptr, compact);
    doc_->Print(&printer);
    return printer.CStr();
}

void Document::save_file(const std::string& filename, bool compact) const {
    if (!doc_) {
        throw XmlException("Cannot save empty document");
    }

    tinyxml2::XMLError error = doc_->SaveFile(filename.c_str(), compact);
    if (error != tinyxml2::XML_SUCCESS) {
        throw XmlException("Failed to save XML file '" + filename + "': " +
                          std::string(doc_->ErrorStr()));
    }
}

Element Document::root() const {
    if (!doc_) return Element(nullptr, nullptr);

    // We need to cast away const to get a shared_ptr<Document>
    auto shared_this = std::const_pointer_cast<Document>(shared_from_this());
    return Element(doc_->RootElement(), shared_this);
}

Element Document::create_root(const std::string& name) {
    if (!doc_) {
        doc_ = std::make_unique<tinyxml2::XMLDocument>();
    }

    // Remove existing root if any
    if (doc_->RootElement()) {
        doc_->DeleteChild(doc_->RootElement());
    }

    auto* root_elem = doc_->NewElement(name.c_str());
    doc_->InsertFirstChild(root_elem);

    auto shared_this = std::static_pointer_cast<Document>(shared_from_this());
    return Element(root_elem, shared_this);
}

Element Document::create_element(const std::string& name) {
    if (!doc_) {
        doc_ = std::make_unique<tinyxml2::XMLDocument>();
    }

    auto* elem = doc_->NewElement(name.c_str());
    auto shared_this = std::static_pointer_cast<Document>(shared_from_this());
    return Element(elem, shared_this);
}

void Document::add_namespace(const std::string& prefix, const std::string& uri) {
    namespaces_[prefix] = uri;
}

void Document::remove_namespace(const std::string& prefix) {
    namespaces_.erase(prefix);
}

std::string Document::resolve_namespace(const std::string& prefix) const {
    auto it = namespaces_.find(prefix);
    return it != namespaces_.end() ? it->second : "";
}

Element Document::find(const std::string& path) const {
    Element root_elem = root();
    return root_elem.find(path);
}

std::vector<Element> Document::find_all(const std::string& path) const {
    Element root_elem = root();
    return root_elem.find_all(path);
}

bool Document::is_valid() const {
    return doc_ && doc_->Error() == tinyxml2::XML_SUCCESS;
}

std::string Document::get_error_message() const {
    return doc_ ? doc_->ErrorStr() : "No document";
}

int Document::get_error_line() const {
    return doc_ ? doc_->ErrorLineNum() : -1;
}

std::shared_ptr<Document> Document::create() {
    return std::shared_ptr<Document>(new Document());
}

std::shared_ptr<Document> Document::create(const std::string& xml_content) {
    return std::shared_ptr<Document>(new Document(xml_content));
}

std::shared_ptr<Document> Document::load(const std::string& filename) {
    auto doc = create();
    doc->load_file(filename);
    return doc;
}

// Builder pattern implementation
ElementBuilder build_element(Document& doc, const std::string& name) {
    return ElementBuilder(doc.create_element(name));
}

ElementBuilder build_element(Element& parent, const std::string& name) {
    return ElementBuilder(parent.append_child(name));
}

// SOAP namespace implementation
namespace soap {

std::string fault_code_to_string(FaultCode code) {
    switch (code) {
        case FaultCode::VERSION_MISMATCH: return "VersionMismatch";
        case FaultCode::MUST_UNDERSTAND: return "MustUnderstand";
        case FaultCode::CLIENT: return "Client";
        case FaultCode::SERVER: return "Server";
        default: return "Server";
    }
}

FaultCode string_to_fault_code(const std::string& code) {
    if (code == "VersionMismatch") return FaultCode::VERSION_MISMATCH;
    if (code == "MustUnderstand") return FaultCode::MUST_UNDERSTAND;
    if (code == "Client") return FaultCode::CLIENT;
    if (code == "Server") return FaultCode::SERVER;
    return FaultCode::SERVER;
}

// SoapFault implementation
SoapFault::SoapFault(FaultCode code, const std::string& reason,
                     const std::string& detail, const std::string& actor)
    : code_(code), reason_(reason), detail_(detail), actor_(actor) {
}

Element SoapFault::to_element(Document& doc) const {
    Element fault = doc.create_element("soap:Fault");

    Element faultcode = fault.append_child("faultcode");
    faultcode.set_text("soap:" + fault_code_to_string(code_));

    Element faultstring = fault.append_child("faultstring");
    faultstring.set_text(reason_);

    if (!actor_.empty()) {
        Element faultactor = fault.append_child("faultactor");
        faultactor.set_text(actor_);
    }

    if (!detail_.empty()) {
        Element detail = fault.append_child("detail");
        detail.set_text(detail_);
    }

    return fault;
}

SoapFault SoapFault::from_element(const Element& fault_elem) {
    std::string code_str = fault_elem.first_child("faultcode").text();
    std::string reason = fault_elem.first_child("faultstring").text();
    std::string actor = fault_elem.first_child("faultactor").text();
    std::string detail = fault_elem.first_child("detail").text();

    // Remove "soap:" prefix if present
    if (code_str.substr(0, 5) == "soap:") {
        code_str = code_str.substr(5);
    }

    FaultCode code = string_to_fault_code(code_str);
    return SoapFault(code, reason, detail, actor);
}

// SoapHeader implementation
SoapHeader::SoapHeader(std::shared_ptr<Document> doc) : doc_(doc) {
}

Element SoapHeader::add_header_block(const std::string& name, const Namespace& ns) {
    if (!doc_) return Element(nullptr, nullptr);

    Element block = doc_->create_element(ns.qualified_name(name));
    if (!ns.uri.empty()) {
        block.set_namespace(ns);
    }

    header_blocks_.push_back(block);
    return block;
}

void SoapHeader::add_header_block(const Element& block) {
    header_blocks_.push_back(block);
}

void SoapHeader::remove_header_block(const std::string& name, const Namespace& ns) {
    std::string qualified = ns.qualified_name(name);
    header_blocks_.erase(
        std::remove_if(header_blocks_.begin(), header_blocks_.end(),
                      [&qualified](const Element& elem) {
                          return elem.name() == qualified;
                      }),
        header_blocks_.end());
}

void SoapHeader::remove_header_block(const Element& block) {
    header_blocks_.erase(
        std::remove_if(header_blocks_.begin(), header_blocks_.end(),
                      [&block](const Element& elem) {
                          return elem.get_tinyxml_element() == block.get_tinyxml_element();
                      }),
        header_blocks_.end());
}

Element SoapHeader::find_header_block(const std::string& name, const Namespace& ns) const {
    std::string qualified = ns.qualified_name(name);
    for (const auto& block : header_blocks_) {
        if (block.name() == qualified) {
            return block;
        }
    }
    return Element(nullptr, doc_);
}

std::vector<Element> SoapHeader::find_header_blocks(const std::string& name, const Namespace& ns) const {
    std::vector<Element> result;
    std::string qualified = ns.qualified_name(name);
    for (const auto& block : header_blocks_) {
        if (block.name() == qualified) {
            result.push_back(block);
        }
    }
    return result;
}

Element SoapHeader::to_element() const {
    if (!doc_) return Element(nullptr, nullptr);

    Element header = doc_->create_element("soap:Header");
    for (const auto& block : header_blocks_) {
        // In a real implementation, you'd need to properly clone/move the elements
        // This is a simplified version
    }

    return header;
}

SoapHeader SoapHeader::from_element(const Element& header_elem, std::shared_ptr<Document> doc) {
    SoapHeader header(doc);

    for (const auto& child : header_elem.children()) {
        header.add_header_block(child);
    }

    return header;
}

} // namespace soap

} // namespace xml_cpp
