#pragma once

#include "mongoose_cpp.hpp"
#include "xml_wrapper.hpp"
#include <memory>
#include <string>
#include <vector>
#include <map>
#include <functional>
#include <chrono>
#include <random>
#include <sstream>
#include <iomanip>

namespace onvif {

// ONVIF Device Information
struct DeviceInfo {
    std::string manufacturer = "ONVIF Device";
    std::string model = "Generic Camera";
    std::string firmware_version = "1.0.0";
    std::string serial_number = "123456789";
    std::string hardware_id = "HW001";
    std::string device_id;
    std::vector<std::string> scopes;
    std::vector<std::string> types;
    std::vector<std::string> xaddrs;
    int metadata_version = 1;
    
    DeviceInfo() {
        // Generate a random device ID
        device_id = generate_uuid();
        
        // Default scopes
        scopes.push_back("onvif://www.onvif.org/type/NetworkVideoTransmitter");
        scopes.push_back("onvif://www.onvif.org/hardware/" + model);
        scopes.push_back("onvif://www.onvif.org/location/country/unknown");
        
        // Default types
        types.push_back("dn:NetworkVideoTransmitter");
        types.push_back("tds:Device");
    }
    
private:
    std::string generate_uuid();
};

// Media Profile Information
struct MediaProfile {
    std::string token;
    std::string name;
    bool fixed = false;
    
    // Video configuration
    std::string video_source_token;
    std::string video_encoder_token;
    
    // Audio configuration (optional)
    std::string audio_source_token;
    std::string audio_encoder_token;
    
    // PTZ configuration (optional)
    std::string ptz_token;
    
    MediaProfile(const std::string& token, const std::string& name)
        : token(token), name(name) {}
};

// Video Source Information
struct VideoSource {
    std::string token;
    std::string name;
    int width = 1920;
    int height = 1080;
    double framerate = 25.0;
    
    VideoSource(const std::string& token, const std::string& name)
        : token(token), name(name) {}
};

// ONVIF Service Endpoints
enum class ServiceType {
    DEVICE_MANAGEMENT,
    MEDIA,
    PTZ,
    IMAGING,
    ANALYTICS,
    EVENTS
};

struct ServiceEndpoint {
    ServiceType type;
    std::string namespace_uri;
    std::string xaddr;
    std::string version;
    
    ServiceEndpoint(ServiceType t, const std::string& ns, const std::string& addr, const std::string& ver)
        : type(t), namespace_uri(ns), xaddr(addr), version(ver) {}
};

// ONVIF Server Configuration
struct ServerConfig {
    std::string listen_address = "0.0.0.0";
    int http_port = 8080;
    int discovery_port = 3702;
    std::string device_service_path = "/onvif/device_service";
    std::string media_service_path = "/onvif/media_service";
    std::string ptz_service_path = "/onvif/ptz_service";
    bool enable_discovery = true;
    bool enable_authentication = false;
    std::string username = "admin";
    std::string password = "admin";
};

// Forward declarations
class OnvifServer;
class DeviceService;
class MediaService;
class DiscoveryService;

// Base class for ONVIF services
class OnvifService {
protected:
    OnvifServer* server_;
    std::string service_path_;
    std::string namespace_uri_;
    
public:
    OnvifService(OnvifServer* server, const std::string& path, const std::string& ns)
        : server_(server), service_path_(path), namespace_uri_(ns) {}
    
    virtual ~OnvifService() = default;
    
    virtual void handle_request(const mongoose_cpp::http::Message& request, 
                               mongoose_cpp::http::Response& response) = 0;
    
    const std::string& get_service_path() const { return service_path_; }
    const std::string& get_namespace_uri() const { return namespace_uri_; }

protected:
    // Helper methods for SOAP processing
    xml_cpp::soap::SoapEnvelope parse_soap_request(const std::string& body);
    std::string create_soap_response(const xml_cpp::Element& body_content);
    std::string create_soap_fault(xml_cpp::soap::FaultCode code, const std::string& reason, 
                                 const std::string& detail = "");
    
    // Authentication helpers
    bool authenticate_request(const mongoose_cpp::http::Message& request);
    std::string extract_action(const xml_cpp::soap::SoapEnvelope& envelope);
};

// Device Management Service
class DeviceService : public OnvifService {
private:
    DeviceInfo device_info_;
    std::vector<ServiceEndpoint> service_endpoints_;
    
public:
    DeviceService(OnvifServer* server, const std::string& path);
    
    void handle_request(const mongoose_cpp::http::Message& request, 
                       mongoose_cpp::http::Response& response) override;
    
    void set_device_info(const DeviceInfo& info) { device_info_ = info; }
    const DeviceInfo& get_device_info() const { return device_info_; }
    
    void add_service_endpoint(const ServiceEndpoint& endpoint);
    
private:
    // Device service operations
    xml_cpp::Element handle_get_device_information(const xml_cpp::Element& request);
    xml_cpp::Element handle_get_capabilities(const xml_cpp::Element& request);
    xml_cpp::Element handle_get_services(const xml_cpp::Element& request);
    xml_cpp::Element handle_get_system_date_and_time(const xml_cpp::Element& request);
    xml_cpp::Element handle_set_system_date_and_time(const xml_cpp::Element& request);
    xml_cpp::Element handle_get_scopes(const xml_cpp::Element& request);
    xml_cpp::Element handle_set_scopes(const xml_cpp::Element& request);
};

// Media Service
class MediaService : public OnvifService {
private:
    std::vector<MediaProfile> profiles_;
    std::vector<VideoSource> video_sources_;
    
public:
    MediaService(OnvifServer* server, const std::string& path);
    
    void handle_request(const mongoose_cpp::http::Message& request, 
                       mongoose_cpp::http::Response& response) override;
    
    void add_media_profile(const MediaProfile& profile);
    void add_video_source(const VideoSource& source);
    
private:
    // Media service operations
    xml_cpp::Element handle_get_profiles(const xml_cpp::Element& request);
    xml_cpp::Element handle_get_profile(const xml_cpp::Element& request);
    xml_cpp::Element handle_create_profile(const xml_cpp::Element& request);
    xml_cpp::Element handle_delete_profile(const xml_cpp::Element& request);
    xml_cpp::Element handle_get_video_sources(const xml_cpp::Element& request);
    xml_cpp::Element handle_get_stream_uri(const xml_cpp::Element& request);
    xml_cpp::Element handle_get_snapshot_uri(const xml_cpp::Element& request);
};

// WS-Discovery Service for device discovery
class DiscoveryService {
private:
    OnvifServer* server_;
    mongoose_cpp::Manager* udp_manager_;
    std::shared_ptr<mongoose_cpp::Connection> udp_listener_;
    DeviceInfo device_info_;
    std::string multicast_address_ = "***************:3702";
    
public:
    DiscoveryService(OnvifServer* server);
    ~DiscoveryService();
    
    void start();
    void stop();
    void set_device_info(const DeviceInfo& info) { device_info_ = info; }
    
private:
    void handle_udp_message(mongoose_cpp::Connection& conn, int event, void* event_data);
    void handle_probe_request(const std::string& message, const mongoose_cpp::Address& from);
    std::string create_probe_response();
    void send_probe_match(const mongoose_cpp::Address& to);
};

// Main ONVIF Server class
class OnvifServer {
private:
    ServerConfig config_;
    mongoose_cpp::Manager manager_;
    mongoose_cpp::http::Server http_server_;
    
    std::unique_ptr<DeviceService> device_service_;
    std::unique_ptr<MediaService> media_service_;
    std::unique_ptr<DiscoveryService> discovery_service_;
    
    DeviceInfo device_info_;
    bool running_ = false;
    
public:
    explicit OnvifServer(const ServerConfig& config = ServerConfig{});
    ~OnvifServer();
    
    // Server lifecycle
    void start();
    void stop();
    bool is_running() const { return running_; }
    
    // Configuration
    void set_config(const ServerConfig& config) { config_ = config; }
    const ServerConfig& get_config() const { return config_; }
    
    void set_device_info(const DeviceInfo& info);
    const DeviceInfo& get_device_info() const { return device_info_; }
    
    // Service access
    DeviceService* get_device_service() { return device_service_.get(); }
    MediaService* get_media_service() { return media_service_.get(); }
    DiscoveryService* get_discovery_service() { return discovery_service_.get(); }
    
    // Manager access for services
    mongoose_cpp::Manager& get_manager() { return manager_; }
    
private:
    void setup_routes();
    void setup_services();
    void handle_wsdl_request(const mongoose_cpp::http::Message& request, 
                            mongoose_cpp::http::Response& response);
};

// Utility functions
std::string generate_uuid();
std::string get_current_timestamp();
std::string url_encode(const std::string& str);
std::string url_decode(const std::string& str);
std::string base64_encode(const std::string& str);
std::string base64_decode(const std::string& str);

} // namespace onvif
