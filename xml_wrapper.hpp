#pragma once

#include <memory>
#include <string>
#include <vector>
#include <map>
#include <functional>
#include <stdexcept>
#include <sstream>
#include <initializer_list>

// Include tinyxml2
#include "xml/tinyxml2.h"

namespace xml_cpp {

// Forward declarations
class Document;
class Element;
class Attribute;
class Text;

// Exception classes
class XmlException : public std::runtime_error {
public:
    explicit XmlException(const std::string& message) 
        : std::runtime_error("XML Error: " + message) {}
};

class ParseException : public XmlException {
public:
    explicit ParseException(const std::string& message, int line = -1) 
        : XmlException("Parse Error" + (line > 0 ? " (line " + std::to_string(line) + ")" : "") + ": " + message) {}
};

class ValidationException : public XmlException {
public:
    explicit ValidationException(const std::string& message) 
        : XmlException("Validation Error: " + message) {}
};

// Utility classes
class XPath {
private:
    std::string path_;
    
public:
    explicit XPath(const std::string& path) : path_(path) {}
    const std::string& path() const { return path_; }
    
    // XPath builder methods
    static XPath root() { return XPath("/"); }
    static XPath element(const std::string& name) { return XPath("//" + name); }
    XPath child(const std::string& name) const { return XPath(path_ + "/" + name); }
    XPath attribute(const std::string& name) const { return XPath(path_ + "/@" + name); }
    XPath index(int idx) const { return XPath(path_ + "[" + std::to_string(idx + 1) + "]"); }
};

// Namespace management
class Namespace {
public:
    std::string prefix;
    std::string uri;
    
    Namespace() = default;
    Namespace(const std::string& prefix, const std::string& uri) : prefix(prefix), uri(uri) {}
    
    std::string qualified_name(const std::string& local_name) const {
        return prefix.empty() ? local_name : prefix + ":" + local_name;
    }
};

using NamespaceMap = std::map<std::string, std::string>; // prefix -> uri

// Attribute wrapper
class Attribute {
private:
    tinyxml2::XMLAttribute* attr_;
    
public:
    explicit Attribute(tinyxml2::XMLAttribute* attr) : attr_(attr) {}
    
    bool is_valid() const { return attr_ != nullptr; }
    std::string name() const { return attr_ ? attr_->Name() : ""; }
    std::string value() const { return attr_ ? attr_->Value() : ""; }
    
    // Type-safe value getters
    int int_value(int default_val = 0) const;
    double double_value(double default_val = 0.0) const;
    bool bool_value(bool default_val = false) const;
    
    // Value setters
    void set_value(const std::string& value);
    void set_value(int value);
    void set_value(double value);
    void set_value(bool value);
    
    // Internal access
    tinyxml2::XMLAttribute* get_tinyxml_attribute() { return attr_; }
    const tinyxml2::XMLAttribute* get_tinyxml_attribute() const { return attr_; }
};

// Text wrapper
class Text {
private:
    tinyxml2::XMLText* text_;
    
public:
    explicit Text(tinyxml2::XMLText* text) : text_(text) {}
    
    bool is_valid() const { return text_ != nullptr; }
    std::string value() const { return text_ ? text_->Value() : ""; }
    void set_value(const std::string& value);
    
    // Internal access
    tinyxml2::XMLText* get_tinyxml_text() { return text_; }
    const tinyxml2::XMLText* get_tinyxml_text() const { return text_; }
};

// Element wrapper
class Element {
private:
    tinyxml2::XMLElement* elem_;
    std::shared_ptr<Document> doc_; // Keep document alive
    
public:
    Element(tinyxml2::XMLElement* elem, std::shared_ptr<Document> doc);
    
    bool is_valid() const { return elem_ != nullptr; }
    std::string name() const { return elem_ ? elem_->Name() : ""; }
    std::string text() const { return elem_ ? (elem_->GetText() ? elem_->GetText() : "") : ""; }
    
    // Attribute access
    bool has_attribute(const std::string& name) const;
    Attribute get_attribute(const std::string& name) const;
    std::string get_attribute_value(const std::string& name, const std::string& default_val = "") const;
    Element& set_attribute(const std::string& name, const std::string& value);
    Element& set_attribute(const std::string& name, int value);
    Element& set_attribute(const std::string& name, double value);
    Element& set_attribute(const std::string& name, bool value);
    Element& remove_attribute(const std::string& name);
    
    // Text content
    Element& set_text(const std::string& text);
    Element& append_text(const std::string& text);
    
    // Child element access
    Element first_child(const std::string& name = "") const;
    Element next_sibling(const std::string& name = "") const;
    Element parent() const;
    std::vector<Element> children(const std::string& name = "") const;
    
    // Child element creation
    Element append_child(const std::string& name);
    Element prepend_child(const std::string& name);
    Element insert_child_after(const Element& after, const std::string& name);
    Element insert_child_before(const Element& before, const std::string& name);
    
    // Element removal
    void remove_child(const Element& child);
    void remove_children(const std::string& name = "");
    void remove_from_parent();
    
    // XPath-like queries (simplified)
    Element find(const std::string& path) const;
    std::vector<Element> find_all(const std::string& path) const;
    
    // Namespace support
    Element& set_namespace(const Namespace& ns);
    std::string get_namespace_uri() const;
    std::string get_namespace_prefix() const;
    
    // Utility methods
    bool empty() const { return !is_valid() || (!elem_->FirstChild() && !elem_->GetText()); }
    size_t child_count() const;
    
    // Internal access
    tinyxml2::XMLElement* get_tinyxml_element() { return elem_; }
    const tinyxml2::XMLElement* get_tinyxml_element() const { return elem_; }
    std::shared_ptr<Document> get_document() const { return doc_; }
};

// Document wrapper
class Document {
private:
    std::unique_ptr<tinyxml2::XMLDocument> doc_;
    NamespaceMap namespaces_;
    
public:
    Document();
    explicit Document(const std::string& xml_content);
    ~Document() = default;
    
    // Move semantics
    Document(Document&& other) noexcept;
    Document& operator=(Document&& other) noexcept;
    
    // Copy is disabled to avoid issues with tinyxml2
    Document(const Document&) = delete;
    Document& operator=(const Document&) = delete;
    
    // Parsing
    void parse(const std::string& xml_content);
    void load_file(const std::string& filename);
    
    // Serialization
    std::string to_string(bool compact = false) const;
    void save_file(const std::string& filename, bool compact = false) const;
    
    // Root element access
    Element root() const;
    Element create_root(const std::string& name);
    
    // Element creation (unlinked)
    Element create_element(const std::string& name);
    
    // Namespace management
    void add_namespace(const std::string& prefix, const std::string& uri);
    void remove_namespace(const std::string& prefix);
    const NamespaceMap& namespaces() const { return namespaces_; }
    std::string resolve_namespace(const std::string& prefix) const;
    
    // XPath-like queries
    Element find(const std::string& path) const;
    std::vector<Element> find_all(const std::string& path) const;
    
    // Validation
    bool is_valid() const;
    std::string get_error_message() const;
    int get_error_line() const;
    
    // Internal access
    tinyxml2::XMLDocument* get_tinyxml_document() { return doc_.get(); }
    const tinyxml2::XMLDocument* get_tinyxml_document() const { return doc_.get(); }
    
    // Factory method for shared ownership
    static std::shared_ptr<Document> create();
    static std::shared_ptr<Document> create(const std::string& xml_content);
    static std::shared_ptr<Document> load(const std::string& filename);
};

// Builder pattern for XML construction
class ElementBuilder {
private:
    Element element_;
    
public:
    explicit ElementBuilder(Element element) : element_(std::move(element)) {}
    
    ElementBuilder& attr(const std::string& name, const std::string& value) {
        element_.set_attribute(name, value);
        return *this;
    }
    
    ElementBuilder& attr(const std::string& name, int value) {
        element_.set_attribute(name, value);
        return *this;
    }
    
    ElementBuilder& attr(const std::string& name, double value) {
        element_.set_attribute(name, value);
        return *this;
    }
    
    ElementBuilder& attr(const std::string& name, bool value) {
        element_.set_attribute(name, value);
        return *this;
    }
    
    ElementBuilder& text(const std::string& text) {
        element_.set_text(text);
        return *this;
    }
    
    ElementBuilder child(const std::string& name, std::function<void(ElementBuilder&)> builder = nullptr) {
        Element child_elem = element_.append_child(name);
        if (builder) {
            ElementBuilder child_builder(child_elem);
            builder(child_builder);
        }
        return *this;
    }
    
    Element build() { return element_; }
    operator Element() { return element_; }
};

// Convenience functions
ElementBuilder build_element(Document& doc, const std::string& name);
ElementBuilder build_element(Element& parent, const std::string& name);

// SOAP message handling classes
namespace soap {

// SOAP Fault codes
enum class FaultCode {
    VERSION_MISMATCH,
    MUST_UNDERSTAND,
    CLIENT,
    SERVER
};

std::string fault_code_to_string(FaultCode code);
FaultCode string_to_fault_code(const std::string& code);

// SOAP Fault class
class SoapFault {
private:
    FaultCode code_;
    std::string reason_;
    std::string detail_;
    std::string actor_;

public:
    SoapFault(FaultCode code, const std::string& reason,
              const std::string& detail = "", const std::string& actor = "");

    FaultCode code() const { return code_; }
    const std::string& reason() const { return reason_; }
    const std::string& detail() const { return detail_; }
    const std::string& actor() const { return actor_; }

    void set_code(FaultCode code) { code_ = code; }
    void set_reason(const std::string& reason) { reason_ = reason; }
    void set_detail(const std::string& detail) { detail_ = detail; }
    void set_actor(const std::string& actor) { actor_ = actor; }

    // Convert to XML element
    Element to_element(Document& doc) const;

    // Create from XML element
    static SoapFault from_element(const Element& fault_elem);
};

// SOAP Header class
class SoapHeader {
private:
    std::vector<Element> header_blocks_;
    std::shared_ptr<Document> doc_;

public:
    explicit SoapHeader(std::shared_ptr<Document> doc);

    // Add header block
    Element add_header_block(const std::string& name, const Namespace& ns = Namespace());
    void add_header_block(const Element& block);

    // Remove header block
    void remove_header_block(const std::string& name, const Namespace& ns = Namespace());
    void remove_header_block(const Element& block);

    // Get header blocks
    const std::vector<Element>& header_blocks() const { return header_blocks_; }
    Element find_header_block(const std::string& name, const Namespace& ns = Namespace()) const;
    std::vector<Element> find_header_blocks(const std::string& name, const Namespace& ns = Namespace()) const;

    // Check if header is empty
    bool empty() const { return header_blocks_.empty(); }

    // Convert to XML element
    Element to_element() const;

    // Create from XML element
    static SoapHeader from_element(const Element& header_elem, std::shared_ptr<Document> doc);
};

// SOAP Body class
class SoapBody {
private:
    std::vector<Element> body_elements_;
    std::shared_ptr<Document> doc_;
    std::unique_ptr<SoapFault> fault_;

public:
    explicit SoapBody(std::shared_ptr<Document> doc);

    // Add body element
    Element add_element(const std::string& name, const Namespace& ns = Namespace());
    void add_element(const Element& elem);

    // Remove body element
    void remove_element(const Element& elem);
    void clear_elements();

    // Get body elements
    const std::vector<Element>& elements() const { return body_elements_; }
    Element first_element() const;

    // Fault handling
    void set_fault(const SoapFault& fault);
    void clear_fault();
    bool has_fault() const { return fault_ != nullptr; }
    const SoapFault* fault() const { return fault_.get(); }

    // Check if body is empty
    bool empty() const { return body_elements_.empty() && !has_fault(); }

    // Convert to XML element
    Element to_element() const;

    // Create from XML element
    static SoapBody from_element(const Element& body_elem, std::shared_ptr<Document> doc);
};

// SOAP Envelope class
class SoapEnvelope {
public:
    enum class Version {
        SOAP_1_1,
        SOAP_1_2
    };

private:
    Version version_;
    std::unique_ptr<SoapHeader> header_;
    std::unique_ptr<SoapBody> body_;
    std::shared_ptr<Document> doc_;
    NamespaceMap additional_namespaces_;

public:
    explicit SoapEnvelope(Version version = Version::SOAP_1_2);
    explicit SoapEnvelope(std::shared_ptr<Document> doc, Version version = Version::SOAP_1_2);

    // Version management
    Version version() const { return version_; }
    void set_version(Version version) { version_ = version; }
    std::string version_namespace() const;

    // Header management
    SoapHeader& header();
    const SoapHeader& header() const;
    bool has_header() const { return header_ && !header_->empty(); }
    void remove_header();

    // Body management
    SoapBody& body();
    const SoapBody& body() const;

    // Namespace management
    void add_namespace(const std::string& prefix, const std::string& uri);
    void remove_namespace(const std::string& prefix);
    const NamespaceMap& additional_namespaces() const { return additional_namespaces_; }

    // Document access
    std::shared_ptr<Document> document() const { return doc_; }

    // Serialization
    std::string to_string(bool compact = false) const;
    void save_file(const std::string& filename, bool compact = false) const;

    // Parsing
    static SoapEnvelope from_string(const std::string& xml_content);
    static SoapEnvelope from_file(const std::string& filename);
    static SoapEnvelope from_document(std::shared_ptr<Document> doc);

    // Validation
    bool is_valid() const;
    std::string get_validation_error() const;

    // Convert to XML document
    std::shared_ptr<Document> to_document() const;
};

} // namespace soap

// HTTP XML response handling
namespace http {

// HTTP status codes commonly used with XML responses
enum class Status {
    OK = 200,
    CREATED = 201,
    ACCEPTED = 202,
    NO_CONTENT = 204,
    BAD_REQUEST = 400,
    UNAUTHORIZED = 401,
    FORBIDDEN = 403,
    NOT_FOUND = 404,
    METHOD_NOT_ALLOWED = 405,
    CONFLICT = 409,
    INTERNAL_SERVER_ERROR = 500,
    NOT_IMPLEMENTED = 501,
    SERVICE_UNAVAILABLE = 503
};

std::string status_to_string(Status status);
std::string status_reason_phrase(Status status);

// HTTP XML Response class
class XmlResponse {
private:
    Status status_;
    std::map<std::string, std::string> headers_;
    std::shared_ptr<Document> doc_;
    std::string content_type_;
    std::string charset_;

public:
    explicit XmlResponse(Status status = Status::OK);
    explicit XmlResponse(std::shared_ptr<Document> doc, Status status = Status::OK);

    // Status management
    Status status() const { return status_; }
    void set_status(Status status) { status_ = status; }
    int status_code() const { return static_cast<int>(status_); }
    std::string status_text() const { return status_reason_phrase(status_); }

    // Header management
    void set_header(const std::string& name, const std::string& value);
    void add_header(const std::string& name, const std::string& value);
    void remove_header(const std::string& name);
    std::string get_header(const std::string& name) const;
    bool has_header(const std::string& name) const;
    const std::map<std::string, std::string>& headers() const { return headers_; }

    // Content type management
    void set_content_type(const std::string& content_type) { content_type_ = content_type; }
    void set_charset(const std::string& charset) { charset_ = charset; }
    std::string content_type() const;
    std::string full_content_type() const;

    // Document management
    void set_document(std::shared_ptr<Document> doc) { doc_ = doc; }
    std::shared_ptr<Document> document() const { return doc_; }
    bool has_document() const { return doc_ != nullptr; }

    // XML content
    std::string xml_content(bool compact = false) const;
    void set_xml_content(const std::string& xml_content);

    // Root element access (creates document if needed)
    Element root();
    Element create_root(const std::string& name);

    // Convenience methods for common XML responses
    static XmlResponse success(const std::string& message = "");
    static XmlResponse error(Status status, const std::string& message, const std::string& code = "");
    static XmlResponse not_found(const std::string& resource = "");
    static XmlResponse bad_request(const std::string& message = "");
    static XmlResponse internal_error(const std::string& message = "");

    // SOAP response helpers
    static XmlResponse soap_response(const soap::SoapEnvelope& envelope);
    static XmlResponse soap_fault(soap::FaultCode code, const std::string& reason,
                                  const std::string& detail = "");

    // Serialization for HTTP response
    std::string to_http_response() const;
    std::string headers_string() const;

    // Content length
    size_t content_length() const;

    // Validation
    bool is_valid() const;
    std::string get_validation_error() const;
};

} // namespace http

// ONVIF specific XML handling
namespace onvif {

// Common ONVIF namespaces
struct Namespaces {
    static const Namespace SOAP_ENV;
    static const Namespace SOAP_ENC;
    static const Namespace XSI;
    static const Namespace XSD;
    static const Namespace WSDL;
    static const Namespace WSOAP12;
    static const Namespace HTTP;
    static const Namespace MIME;
    static const Namespace DIME;
    static const Namespace WSSE;
    static const Namespace WSU;
    static const Namespace WSA;
    static const Namespace WSDD;
    static const Namespace TDS;
    static const Namespace TRT;
    static const Namespace TRV;
    static const Namespace TLS;
    static const Namespace TIM;
    static const Namespace TAN;
    static const Namespace TRC;
    static const Namespace TRE;
    static const Namespace TRP;
    static const Namespace TRS;
    static const Namespace TT;
    static const Namespace ONVIF;
};

// ONVIF device types
enum class DeviceType {
    NETWORK_VIDEO_TRANSMITTER,
    NETWORK_VIDEO_DISPLAY,
    NETWORK_VIDEO_STORAGE,
    NETWORK_VIDEO_ANALYTICS,
    NETWORK_VIDEO_DOOR_STATION
};

std::string device_type_to_string(DeviceType type);
DeviceType string_to_device_type(const std::string& type);

// ONVIF profile types
enum class ProfileType {
    PROFILE_S,  // Streaming
    PROFILE_G,  // Recording and Storage
    PROFILE_C,  // Access Control
    PROFILE_A,  // Analytics
    PROFILE_T   // Advanced Streaming
};

std::string profile_type_to_string(ProfileType type);
ProfileType string_to_profile_type(const std::string& type);

// ONVIF Device Discovery
class DeviceDiscovery {
private:
    std::string uuid_;
    std::string name_;
    std::string hardware_;
    std::string location_;
    std::vector<std::string> types_;
    std::vector<std::string> scopes_;
    std::vector<std::string> xaddrs_;
    int metadata_version_;

public:
    DeviceDiscovery();

    // Basic properties
    const std::string& uuid() const { return uuid_; }
    void set_uuid(const std::string& uuid) { uuid_ = uuid; }

    const std::string& name() const { return name_; }
    void set_name(const std::string& name) { name_ = name; }

    const std::string& hardware() const { return hardware_; }
    void set_hardware(const std::string& hardware) { hardware_ = hardware; }

    const std::string& location() const { return location_; }
    void set_location(const std::string& location) { location_ = location; }

    // Types
    const std::vector<std::string>& types() const { return types_; }
    void add_type(const std::string& type) { types_.push_back(type); }
    void clear_types() { types_.clear(); }

    // Scopes
    const std::vector<std::string>& scopes() const { return scopes_; }
    void add_scope(const std::string& scope) { scopes_.push_back(scope); }
    void clear_scopes() { scopes_.clear(); }

    // XAddrs (service endpoints)
    const std::vector<std::string>& xaddrs() const { return xaddrs_; }
    void add_xaddr(const std::string& xaddr) { xaddrs_.push_back(xaddr); }
    void clear_xaddrs() { xaddrs_.clear(); }

    // Metadata version
    int metadata_version() const { return metadata_version_; }
    void set_metadata_version(int version) { metadata_version_ = version; }

    // Convert to/from XML
    Element to_probe_match(Document& doc) const;
    static DeviceDiscovery from_probe_match(const Element& probe_match);

    // Create WS-Discovery messages
    static soap::SoapEnvelope create_probe_message();
    static soap::SoapEnvelope create_probe_response(const std::vector<DeviceDiscovery>& devices);
};

// ONVIF Media Profile
class MediaProfile {
private:
    std::string token_;
    std::string name_;
    bool fixed_;

    // Video configuration
    std::string video_source_token_;
    std::string video_encoder_token_;

    // Audio configuration
    std::string audio_source_token_;
    std::string audio_encoder_token_;

    // PTZ configuration
    std::string ptz_token_;

    // Analytics configuration
    std::string analytics_token_;

    // Metadata configuration
    std::string metadata_token_;

public:
    MediaProfile();
    explicit MediaProfile(const std::string& token);

    // Basic properties
    const std::string& token() const { return token_; }
    void set_token(const std::string& token) { token_ = token; }

    const std::string& name() const { return name_; }
    void set_name(const std::string& name) { name_ = name; }

    bool fixed() const { return fixed_; }
    void set_fixed(bool fixed) { fixed_ = fixed; }

    // Video configuration
    const std::string& video_source_token() const { return video_source_token_; }
    void set_video_source_token(const std::string& token) { video_source_token_ = token; }

    const std::string& video_encoder_token() const { return video_encoder_token_; }
    void set_video_encoder_token(const std::string& token) { video_encoder_token_ = token; }

    // Audio configuration
    const std::string& audio_source_token() const { return audio_source_token_; }
    void set_audio_source_token(const std::string& token) { audio_source_token_ = token; }

    const std::string& audio_encoder_token() const { return audio_encoder_token_; }
    void set_audio_encoder_token(const std::string& token) { audio_encoder_token_ = token; }

    // PTZ configuration
    const std::string& ptz_token() const { return ptz_token_; }
    void set_ptz_token(const std::string& token) { ptz_token_ = token; }
    bool has_ptz() const { return !ptz_token_.empty(); }

    // Analytics configuration
    const std::string& analytics_token() const { return analytics_token_; }
    void set_analytics_token(const std::string& token) { analytics_token_ = token; }
    bool has_analytics() const { return !analytics_token_.empty(); }

    // Metadata configuration
    const std::string& metadata_token() const { return metadata_token_; }
    void set_metadata_token(const std::string& token) { metadata_token_ = token; }
    bool has_metadata() const { return !metadata_token_.empty(); }

    // Convert to/from XML
    Element to_element(Document& doc) const;
    static MediaProfile from_element(const Element& profile_elem);
};

} // namespace onvif

// XML validation and formatting utilities
namespace utils {

// XML formatting options
struct FormatOptions {
    bool pretty_print = true;
    int indent_size = 2;
    char indent_char = ' ';
    bool omit_xml_declaration = false;
    std::string encoding = "UTF-8";
    bool standalone = false;

    FormatOptions() = default;

    static FormatOptions compact() {
        FormatOptions opts;
        opts.pretty_print = false;
        return opts;
    }

    static FormatOptions pretty() {
        FormatOptions opts;
        opts.pretty_print = true;
        return opts;
    }
};

// XML Formatter class
class XmlFormatter {
private:
    FormatOptions options_;

public:
    explicit XmlFormatter(const FormatOptions& options = FormatOptions());

    // Format XML string
    std::string format(const std::string& xml_content) const;

    // Format document
    std::string format(const Document& doc) const;

    // Format element
    std::string format(const Element& elem) const;

    // Minify XML (remove unnecessary whitespace)
    static std::string minify(const std::string& xml_content);

    // Pretty print XML
    static std::string pretty_print(const std::string& xml_content, int indent_size = 2);

    // Options management
    const FormatOptions& options() const { return options_; }
    void set_options(const FormatOptions& options) { options_ = options; }
};

// XML Validator class (basic validation)
class XmlValidator {
public:
    struct ValidationResult {
        bool valid = false;
        std::string error_message;
        int error_line = -1;
        int error_column = -1;

        ValidationResult() = default;
        ValidationResult(bool v) : valid(v) {}
        ValidationResult(const std::string& error, int line = -1, int col = -1)
            : valid(false), error_message(error), error_line(line), error_column(col) {}

        operator bool() const { return valid; }
    };

    // Validate XML string
    static ValidationResult validate(const std::string& xml_content);

    // Validate document
    static ValidationResult validate(const Document& doc);

    // Check if XML is well-formed
    static bool is_well_formed(const std::string& xml_content);

    // Check SOAP envelope structure
    static ValidationResult validate_soap_envelope(const soap::SoapEnvelope& envelope);

    // Check ONVIF message structure
    static ValidationResult validate_onvif_message(const Document& doc);
};

// XML utilities
class XmlUtils {
public:
    // Escape XML special characters
    static std::string escape_xml(const std::string& text);

    // Unescape XML special characters
    static std::string unescape_xml(const std::string& text);

    // Encode text for XML attribute
    static std::string encode_attribute(const std::string& text);

    // Decode XML attribute text
    static std::string decode_attribute(const std::string& text);

    // Generate UUID for XML elements
    static std::string generate_uuid();

    // Generate timestamp in XML format
    static std::string generate_timestamp();

    // Parse XML timestamp
    static std::time_t parse_timestamp(const std::string& timestamp);

    // Convert between different XML encodings
    static std::string convert_encoding(const std::string& xml_content,
                                       const std::string& from_encoding,
                                       const std::string& to_encoding);

    // Extract text content from mixed content elements
    static std::string extract_text_content(const Element& elem);

    // Compare XML documents (ignoring formatting)
    static bool xml_equal(const Document& doc1, const Document& doc2);
    static bool xml_equal(const std::string& xml1, const std::string& xml2);

    // Merge XML documents
    static Document merge_documents(const Document& base, const Document& overlay);

    // Clone document
    static std::shared_ptr<Document> clone_document(const Document& doc);
};

} // namespace utils

} // namespace xml_cpp
